import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useTranslations } from '@/lib/i18n/typed-translations'
import { useIsMobile } from '@/hooks/use-mobile'
import type { PostReviewer } from '@/lib/types/api'

// Utility functions
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getInitials = (name?: string) => {
  if (!name) return '??'
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

interface ReviewerCardProps {
  reviewer: PostReviewer
  showDetails?: boolean
  compact?: boolean
  className?: string
}

export function ReviewerCard({
  reviewer,
  showDetails = true,
  compact = false,
  className
}: ReviewerCardProps) {
  const { t, keys } = useTranslations()
  const isMobile = useIsMobile()

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "pending": return "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-200 dark:border-yellow-800"
      case "approved": return "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-200 dark:border-green-800"
      case "rework": return "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-200 dark:border-red-800"
      default: return "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-200 dark:border-gray-800"
    }
  }

  const getBorderColor = (status?: string) => {
    switch (status) {
      case "pending": return "border-l-yellow-500"
      case "approved": return "border-l-green-500"
      case "rework": return "border-l-red-500"
      default: return "border-l-gray-500"
    }
  }

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case "pending": return <Clock className="h-3 w-3" />
      case "approved": return <CheckCircle className="h-3 w-3" />
      case "rework": return <XCircle className="h-3 w-3" />
      default: return <AlertCircle className="h-3 w-3" />
    }
  }

  const getStatusLabel = (status?: string) => {
    switch (status) {
      case "pending": return t(keys.collaborationHubs.posts.review.statusPending)
      case "approved": return t(keys.collaborationHubs.posts.review.statusApproved)
      case "rework": return t(keys.collaborationHubs.posts.review.statusRework)
      default: return status || 'Unknown'
    }
  }

  const hasReviewNotes = reviewer.review_notes && reviewer.review_notes.trim().length > 0
  const hasReviewedAt = reviewer.reviewed_at
  const isPending = reviewer.status === 'pending'

  if (compact) {
    return (
      <div className={cn("p-2 rounded-md hover:bg-muted/50 transition-colors border-l-2",
        getBorderColor(reviewer.status),
        className)}>
        <div className="flex items-center gap-2">
          <Avatar className={cn("h-6 w-6", isMobile && "h-8 w-8")}>
            <AvatarFallback className={cn("text-xs", isMobile && "text-sm")}>
              {getInitials(reviewer.name)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className={cn("text-sm font-medium truncate", isMobile && "text-base")}>{reviewer.name}</span>
              <Badge variant="outline" className={cn("text-xs shrink-0", getStatusColor(reviewer.status), isMobile && "text-sm")}>
                {getStatusIcon(reviewer.status)}
                <span className="ml-1">{getStatusLabel(reviewer.status)}</span>
              </Badge>
            </div>
            {hasReviewNotes && (
              <div className="mt-1">
                <p className="text-xs text-muted-foreground overflow-hidden"
                   style={{
                     display: '-webkit-box',
                     WebkitLineClamp: 2,
                     WebkitBoxOrient: 'vertical'
                   }}>
                  💬 {reviewer.review_notes}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className={cn("pb-3", isMobile && "pb-2")}>
        <div className="flex items-center gap-3">
          <Avatar className={cn("h-8 w-8", isMobile && "h-10 w-10")}>
            <AvatarFallback className={cn("text-sm", isMobile && "text-base")}>
              {getInitials(reviewer.name)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className={cn("font-medium text-sm truncate", isMobile && "text-base")}>{reviewer.name}</p>
            <p className={cn("text-xs text-muted-foreground truncate", isMobile && "text-sm")}>{reviewer.email}</p>
          </div>
          <Badge variant="outline" className={cn(getStatusColor(reviewer.status), isMobile && "text-sm px-3 py-1")}>
            {getStatusIcon(reviewer.status)}
            <span className="ml-1">{getStatusLabel(reviewer.status)}</span>
          </Badge>
        </div>
      </CardHeader>

      {showDetails && (hasReviewNotes || hasReviewedAt || reviewer.assigned_at) && (
        <CardContent className="pt-0">
          <div className="space-y-3 pt-2">
            {/* Timeline */}
            <div className="space-y-2 text-xs text-muted-foreground">
              {reviewer.assigned_at && (
                <div className="flex items-center gap-2">
                  <Clock className="h-3 w-3" />
                  <span>Assigned {formatDate(reviewer.assigned_at)}</span>
                </div>
              )}
              {hasReviewedAt && (
                <div className="flex items-center gap-2">
                  {getStatusIcon(reviewer.status)}
                  <span>Reviewed {formatDate(reviewer.reviewed_at!)}</span>
                </div>
              )}
              {isPending && (
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-3 w-3 text-yellow-500" />
                  <span className="text-yellow-600 dark:text-yellow-400">Waiting for review</span>
                </div>
              )}
            </div>

            {/* Review Notes - More Prominent */}
            {hasReviewNotes && (
                <div className={cn(
                  "rounded-md p-3 border-l-4",
                  reviewer.status === 'rework'
                    ? "bg-red-50 border-l-red-500 dark:bg-red-950/20"
                    : reviewer.status === 'approved'
                    ? "bg-green-50 border-l-green-500 dark:bg-green-950/20"
                    : "bg-yellow-50 border-l-yellow-500 dark:bg-yellow-950/20"
                )}>
                  <p className="text-sm whitespace-pre-wrap font-medium">
                    {reviewer.review_notes}
                  </p>
                </div>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  )
}
